<template>
  <div class="friend-info-container">
    <div class="friend-info-header">
      <div class="header-layout">
        <!-- 第1部分：头像 -->
        <div class="avatar-section">
          <img :src="friendInfo?.avatar || '../assets/icon.png'" alt="头像" class="avatar" />
        </div>

        <!-- 第2部分：用户信息 -->
        <div class="user-info-section">
          <div class="nickname">{{ friendInfo?.nickname || '暂无昵称' }}</div>
          <div class="qq-info">QQ {{ friendInfo?.userId || '暂无' }}</div>
          <div class="status-info">
            <svg class="icon" aria-hidden="true">
              <use :xlink:href="`#${currentStatusIcon}`"></use>
            </svg>
            <span class="status-text">{{ friendInfo?.status || '离线' }}</span>
          </div>
        </div>

        <!-- 第3部分：点赞区域 -->
        <div class="like-section">
          <svg class="like-icon" aria-hidden="true" @click="handleLike">
            <use xlink:href="#icon-dianzan"></use>
          </svg>
          <div class="like-count">{{ likeCount || 0 }}</div>
        </div>
      </div>
    </div>

    <div class="friend-info-content">
      <div class="friend-details">
        <!-- 基本信息区域 -->
        <div class="basic-info" v-if="hasBasicInfo">
          <span class="info-item" v-if="friendInfo?.gender">
            <svg class="gender-icon" aria-hidden="true">
              <use :xlink:href="`#icon-${friendInfo?.gender === 1 ? 'male' : 'female'}`"></use>
            </svg>
            {{ friendInfo?.gender === 1 ? '男' : '女' }}
          </span>
          <span class="info-item" v-if="calculateAge(friendInfo?.birthday) !== ''">{{ calculateAge(friendInfo?.birthday)
          }}岁</span>
          <span class="info-item" v-if="getConstellation(friendInfo?.birthday)">{{ formatBirthday(friendInfo?.birthday)
          }} {{
              getConstellation(friendInfo?.birthday) }}</span>
          <span class="info-item" v-if="formatAddress(friendInfo?.country, friendInfo?.province)">现居 {{
            formatAddress(friendInfo?.country, friendInfo?.province) }}</span>
        </div>

        <!-- 等级区域 -->
        <div class="level-info" v-if="friendInfo?.grade && friendInfo.grade > 0">
          <span class="level-label">等级</span>
          <div class="level-icons">
            <svg v-for="(icon, index) in levelIcons" :key="index" class="icon level-icon" aria-hidden="true">
              <use :xlink:href="`#${icon}`"></use>
            </svg>
          </div>
        </div>

        <!-- 详细信息列表 -->
        <div class="detail-list">
          <div class="detail-item">
            <span class="detail-label">备注：</span>
            <span class="detail-value">{{ friendInfo?.remark || '暂无备注' }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">分组：</span>
            <span class="detail-value">{{ friendInfo?.group || '我的好友' }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">签名：</span>
            <span class="detail-value">{{ friendInfo?.signiture || '暂无个性签名' }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">朋友圈：</span>
            <span class="detail-value qzone-link" @click="openQzone">朋友圈</span>
          </div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <div class="action-btn share-btn" @click="shareProfile">
        <span>分享</span>
      </div>
      <div class="action-btn call-btn" @click="audioCall">
        <span>音视频通话</span>
      </div>
      <div class="action-btn message-btn primary" @click="sendMessage">
        <span>发消息</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { getFriendInfo } from '../../api/user'
import { statusList } from '../../utils/status'
import { calculateLevelIcons } from '../../utils/global'

const props = defineProps({
  userId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['sendMessage', 'audioCall', 'videoCall'])

const friendInfo = ref(null)
const loading = ref(false)
const likeCount = ref(0)

// 计算当前状态图标
const currentStatusIcon = computed(() => {
  if (!friendInfo.value?.status) return 'icon-dashiqiu'
  const statusItem = statusList.find(item => item.text === friendInfo.value.status)
  return statusItem ? statusItem.icon : 'icon-dashiqiu'
})

// 动态计算等级图标
const levelIcons = computed(() => {
  const grade = friendInfo.value?.grade || 0;
  return calculateLevelIcons(grade);
})

// 判断是否有基本信息数据
const hasBasicInfo = computed(() => {
  if (!friendInfo.value) return false
  return !!(
    friendInfo.value.gender ||
    calculateAge(friendInfo.value.birthday) !== '' ||
    formatBirthday(friendInfo.value.birthday) ||
    getConstellation(friendInfo.value.birthday) ||
    formatAddress(friendInfo.value.country, friendInfo.value.province)
  )
})

// 获取好友信息
const loadFriendInfo = async (userId) => {
  if (!userId) return

  try {
    loading.value = true
    const { data } = await getFriendInfo(userId)
    friendInfo.value = data
    console.log(data);

  } catch (error) {
    console.error('获取好友信息失败:', error)
    friendInfo.value = null
  } finally {
    loading.value = false
  }
}

// 监听userId变化
watch(() => props.userId, (newUserId) => {
  if (newUserId) {
    loadFriendInfo(newUserId)
  } else {
    friendInfo.value = null
  }
}, { immediate: true })

// 操作方法
const sendMessage = () => {
  emit('sendMessage', friendInfo.value)
}

const audioCall = () => {
  emit('audioCall', friendInfo.value)
}

const shareProfile = () => {
  console.log('分享好友资料')
  // TODO: 实现分享功能
}

const videoCall = () => {
  emit('videoCall', friendInfo.value)
}

const editRemark = () => {
  // TODO: 实现修改备注功能
  console.log('修改备注')
}

const viewProfile = () => {
  // TODO: 实现查看资料功能
  console.log('查看资料')
}

const deleteFriend = () => {
  // TODO: 实现删除好友功能
  console.log('删除好友')
}

// 点赞功能
const handleLike = () => {
  likeCount.value++
  console.log('点赞', likeCount.value)
}

// QQ空间功能
const openQzone = () => {
  console.log('打开QQ空间')
  // TODO: 实现打开QQ空间功能
}

// 计算年龄
const calculateAge = (birthday) => {
  if (!birthday) return ''
  try {
    const birthDate = new Date(birthday)
    if (isNaN(birthDate.getTime())) return ''

    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }

    // 确保年龄为非负数且合理
    return age >= 0 && age <= 150 ? age : ''
  } catch (error) {
    console.error('计算年龄时出错:', error)
    return ''
  }
}

// 格式化生日
const formatBirthday = (birthday) => {
  if (!birthday) return ''
  const date = new Date(birthday)
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

// 获取星座
const getConstellation = (birthday) => {
  if (!birthday) return ''
  const date = new Date(birthday)
  const month = date.getMonth() + 1
  const day = date.getDate()

  const constellations = [
    { name: '水瓶座', start: [1, 20], end: [2, 18] },
    { name: '双鱼座', start: [2, 19], end: [3, 20] },
    { name: '白羊座', start: [3, 21], end: [4, 19] },
    { name: '金牛座', start: [4, 20], end: [5, 20] },
    { name: '双子座', start: [5, 21], end: [6, 21] },
    { name: '巨蟹座', start: [6, 22], end: [7, 22] },
    { name: '狮子座', start: [7, 23], end: [8, 22] },
    { name: '处女座', start: [8, 23], end: [9, 22] },
    { name: '天秤座', start: [9, 23], end: [10, 23] },
    { name: '天蝎座', start: [10, 24], end: [11, 22] },
    { name: '射手座', start: [11, 23], end: [12, 21] },
    { name: '摩羯座', start: [12, 22], end: [1, 19] }
  ]

  for (const constellation of constellations) {
    const [startMonth, startDay] = constellation.start
    const [endMonth, endDay] = constellation.end

    if (constellation.name === '摩羯座') {
      if ((month === 12 && day >= startDay) || (month === 1 && day <= endDay)) {
        return constellation.name
      }
    } else {
      if ((month === startMonth && day >= startDay) || (month === endMonth && day <= endDay)) {
        return constellation.name
      }
    }
  }
  return ''
}

// 格式化地址
const formatAddress = (country, province) => {
  if (!country && !province) return ''
  if (country && province) return `${country}-${province}`
  return country || province || ''
}
</script>

<style scoped>
.friend-info-container {
  width: 100%;
  max-width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.friend-info-header {
  padding: 80px 15px 15px;
  border-bottom: 1px solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.header-layout {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 13px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* 第1部分：头像 */
.avatar-section {
  flex-shrink: 0;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

/* 第2部分：用户信息 */
.user-info-section {
  flex: 0 1 auto;
  padding-top: 0;
  min-width: 400px;
  max-width: 400px;
  overflow: hidden;
}

.nickname {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.qq-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #999;
}

.status-text {
  font-size: 14px;
  line-height: 1;
  vertical-align: middle;
}

.icon {
  font-size: 14px;
  vertical-align: middle;
}

/* 第3部分：点赞区域 */
.like-section {
  flex-shrink: 0;
  width: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 0;
}

.like-section .like-icon {
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  color: #666 !important;
  cursor: pointer;
  transition: color 0.2s ease;
  margin-bottom: 4px;
  display: block !important;
}

.like-section .like-icon:hover {
  color: #ff4757;
}

.like-count {
  font-size: 12px;
  color: #999;
  text-align: center;
}

/* 个性签名区域 */
.signature-section {
  margin-top: 15px;
}

.signature {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  word-wrap: break-word;
}

.action-buttons {
  display: flex;
  gap: 8px;
  padding: 16px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #333;
  min-width: 0;
  overflow: hidden;
  height: 32px;
}

.action-btn:hover {
  background: #f5f5f5;
  border-color: #b3b3b3;
}

/* 蓝色主要按钮样式 */
.action-btn.primary {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.action-btn.primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  color: #fff;
}

/* 好友详情外层容器 */
.friend-info-content {
  flex: 1;
  padding: 15px 15px;
  overflow-y: auto;
  background: #fff;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

/* 好友详情容器 */
.friend-details {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 90px 90px;
}

/* 基本信息区域 */
.basic-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  flex-wrap: wrap;
  padding: 4px 0;
  margin-bottom: 4px;
  min-height: 24px;
  max-height: 32px;
}

.info-item {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  font-size: 13px;
  color: #333;
  line-height: 1.2;
  height: 20px;
}

.gender-icon {
  font-size: 12px;
  color: #666;
  width: 12px;
  height: 12px;
}

/* 等级信息区域 */
.level-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  margin-bottom: 4px;
  min-height: 24px;
}

.level-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  flex-shrink: 0;
}

.level-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  justify-content: flex-start;
  align-content: flex-start;
  flex: 1;
}

.level-icons .level-icon {
  font-size: 24px !important;
  color: #ffd700;
  margin: 0 2px;
  padding: 0;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

/* 详细信息列表 */
.detail-list {
  margin-top: 8px;
  padding: 0 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: #666;
  flex-shrink: 0;
  width: 60px;
  font-weight: 400;
}

.detail-value {
  flex: 1;
  color: #333;
  word-wrap: break-word;
  margin-left: 8px;
}

/* QQ空间链接样式 */
.qzone-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;
}

.qzone-link:hover {
  color: #40a9ff;
}
</style>
