<script setup>
const handleMinimize = () => {
  window.electron.ipcRenderer.send('minimize-window')
}

const handleMaximize = () => {
  window.electron.ipcRenderer.send('maximize-window')
}

const handleClose = () => {
  window.electron.ipcRenderer.send('close-window')
}
</script>

<template>
  <div class="window-controls">
    <div class="icon minimize" @click="handleMinimize">
      <svg class="outerColor" aria-hidden="true">
        <use xlink:href="#icon-hengxian"></use>
      </svg>
    </div>
    <div class="icon maximize" @click="handleMaximize">
      <svg class="outerColor" aria-hidden="true">
        <use xlink:href="#icon-fangkuang"></use>
      </svg>
    </div>
    <div class="icon close" @click="handleClose">
      <svg class="outerColor" aria-hidden="true">
        <use xlink:href="#icon-shanchu"></use>
      </svg>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.window-controls {
  position: fixed;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  height: 30px;
  z-index: 9999;
  -webkit-app-region: no-drag;
  background-color: transparent !important;
  border-bottom-left-radius: 3px;

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;
    position: relative;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
      color: #333;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.15);
      transform: scale(0.95);
    }

    /* SVG 图标样式 */
    .outerColor {
      width: 12px;
      height: 12px;
      fill: currentColor;
      pointer-events: none;
      transition: fill 0.2s ease;
    }

    /* 确保use元素继承颜色 */
    use {
      fill: inherit;
    }
  }

  /* 最小化按钮样式 */
  .minimize {
    &:hover {
      background-color: rgba(0, 0, 0, 0.08);
      color: #333;
    }
  }

  /* 最大化按钮样式 */
  .maximize {
    &:hover {
      background-color: rgba(0, 0, 0, 0.08);
      color: #333;
    }
  }

  /* 关闭按钮样式 */
  .close {
    &:hover {
      background-color: #e81123;
      color: #fff;
    }

    &:active {
      background-color: #c50e1f;
      color: #fff;
    }
  }
}
</style>
