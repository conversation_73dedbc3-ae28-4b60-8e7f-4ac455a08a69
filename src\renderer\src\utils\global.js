import { useChatStore } from '../store/chatStore'

/*
 * 会话时间处理
 */
export const datetimeCope = (dateTime) => {
  // 处理时间
  const date = new Date(dateTime)
  const dateYear = date.getFullYear()
  const dateMonth = date.getMonth() + 1
  const dateDay = date.getDate()
  const dateHour = date.getHours()
  const dateMinute = date.getMinutes()

  // 今天
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth() + 1
  const day = today.getDate()
  if (dateYear === year && dateMonth === month && dateDay === day) {
    return `${dateHour}:${dateMinute}`
  }

  // 昨天
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  if (
    dateYear === yesterday.getFullYear() &&
    dateMonth === yesterday.getMonth() + 1 &&
    dateDay === yesterday.getDate()
  ) {
    return `昨天`
  }

  // 是否本周
  if (isDateInThisWeek(date)) {
    return getDayOfWeek(date)
  } else {
    return `${dateYear}年${dateMonth}月${dateDay}日`
  }
}

/**
 * 消息时间处理
 */
export const msgDatetimeCope = (dateTime) => {
  // 处理时间
  const date = new Date(dateTime)
  const dateYear = date.getFullYear()
  const dateMonth = date.getMonth() + 1
  const dateDay = date.getDate()
  const dateHour = date.getHours()
  const dateMinute = date.getMinutes()

  // 今天
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth() + 1
  const day = today.getDate()
  if (dateYear === year && dateMonth === month && dateDay === day) {
    return `${dateHour}:${dateMinute}`
  }

  // 昨天
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  if (
    dateYear === yesterday.getFullYear() &&
    dateMonth === yesterday.getMonth() + 1 &&
    dateDay === yesterday.getDate()
  ) {
    return `昨天 ${dateHour}:${dateMinute}`
  }

  // 是否本周
  if (isDateInThisWeek(date)) {
    return getDayOfWeek(date) + ` ${dateHour}:${dateMinute}`
  } else {
    return `${dateYear}年${dateMonth}月${dateDay}日 ${dateHour}:${dateMinute}`
  }
}

// 判断是否在本周
function isDateInThisWeek(targetDate) {
  const today = new Date() // 当前日期
  const dayOfWeek = today.getDay() // 当前星期几（0为周日）
  const startOfWeek = new Date(today) // 本周开始日期（周一）
  startOfWeek.setDate(today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)) // 处理周日的情况
  startOfWeek.setHours(0, 0, 0, 0) // 时间归零，避免时间差影响

  const endOfWeek = new Date(startOfWeek)
  endOfWeek.setDate(startOfWeek.getDate() + 6) // 本周结束日期（周日）
  endOfWeek.setHours(23, 59, 59, 999) // 设置为周日最后一毫秒

  return targetDate >= startOfWeek && targetDate <= endOfWeek
}
// 星期几
function getDayOfWeek(targetDate) {
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const dayIndex = targetDate.getDay() // 0-6
  return days[dayIndex]
}

/**
 * 是否展示消息的时间
 */
export const isShowTime = (lastTime, currTime) => {
  // 确保时间对象有效
  const lastDate = lastTime instanceof Date ? lastTime : new Date(lastTime)
  const currDate = currTime instanceof Date ? currTime : new Date(currTime)

  // 检查时间对象是否有效
  if (isNaN(lastDate.getTime()) || isNaN(currDate.getTime())) {
    console.warn('Invalid date objects:', { lastTime, currTime })
    return true // 如果时间无效，默认显示时间
  }

  const diff = currDate - lastDate // 时间差毫秒数
  const diffMinutes = diff / 1000 / 60

  // 超过5分钟的消息展示时间
  return diffMinutes > 5
}

/**
 * 获取发送消息相关信息
 */
export const getSendMessageInfo = (chatSessionId) => {
  const chatStore = useChatStore()
  const chatSession = chatStore.getChatSession(chatSessionId)
  return {
    participant: chatSession.participant,
    participantType: chatSession.participantType
  }
}

/**
 * 加群的状态
 */
export const joinGroupStatusEnum = {
  wait: 0, // 等待验证
  agree: 1, // 已同意
  reject: 2 // 已拒绝
}

/**
 * 性别映射关系
 */
export const genderEnum = {
  1: '男',
  2: '女',
  3: '其他'
}

/**
 * 性别反向映射（用于提交数据时转换）
 */
export const genderReverseEnum = {
  '男': 1,
  '女': 2,
  '其他': 3
}

/**
 * 文件类型枚举
 */
export const fileTypeEnum = {
  AVATAR: 0,
  CHAT_IMG: 2,
  GROUP_AVATAR: 3,
  ATTACHMENT: 4
}

/**
 * 计算等级图标
 * @param {number} grade - 等级数值
 * @returns {Array} 等级图标数组
 */
export const calculateLevelIcons = (grade = 0) => {
  const icons = [];

  // 计算各等级图标数量
  const guanjunCount = Math.floor(grade / 16); // 冠军图标数量 (16级一个)
  const jindunCount = Math.floor((grade % 16) / 4); // 金盾图标数量 (4级一个)
  const huangjinCount = grade % 4; // 黄金图标数量 (1级一个)

  // 按优先级添加图标：冠军 > 金盾 > 黄金
  for (let i = 0; i < guanjunCount; i++) {
    icons.push('icon-guanjun');
  }
  for (let i = 0; i < jindunCount; i++) {
    icons.push('icon-jindun');
  }
  for (let i = 0; i < huangjinCount; i++) {
    icons.push('icon-huangjin');
  }

  return icons;
}
