import Axios from './http'

// 获取好友分组列表
export const getFriendGroups = () => {
  return Axios.get('/friend/group/groups')
}

// 获取指定分组下的所有好友信息
export const getGroupFriends = (groupFriendId) => {
  return Axios.get(`/friendships/info/${groupFriendId}`)
}

// 用户置顶群聊
export const getPinnedGroups = () => {
  return Axios.get('/groupMembers/pinned')
}

// 我创建的群聊
export const getCreatedGroups = () => {
  return Axios.get('/groupMembers/created')
}

// 我管理的群聊
export const getManageGroups = () => {
  return Axios.get('/groupMembers/manage')
}

// 我加入的群聊
export const getJoinGroups = () => {
  return Axios.get('/groupMembers/join')
}

// 全网搜索用户
export const searchUsers = (keyword) => {
  return Axios.get('/user/queryUsers', { params: { keyword } })
}

// 添加好友
export const addUser = (data) => {
  return Axios.post('/userInform/add/friend', data)
}

// 好友申请列表
export const getUserInforms = () => {
  return Axios.get('/userInform/informs')
}

// 同意好友
export const agreeFriend = (data) => {
  return Axios.post('/userInform/agree/friend', data)
}

// 拒绝好友
export const rejectFriend = (userInformId) => {
  return Axios.post('/userInform/reject/friend', null, {
    params: {
      userInformId
    }
  })
}


// 修改个人信息
export const updateUserInfo = (data) => {
  return Axios.post('/user/update/my', data)
}

// 修改头像
export const updateAvatar = (imageFile, fileType) => {
  const formData = new FormData();
  formData.append('image', imageFile);
  formData.append('fileType', fileType);

  return Axios.post('/user/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取所有省份
export const getProvinces = () => {
  return Axios.get('/province/all')
}

// 通过省份code获取所有地区
export const getRegions = (provinceCode) => {
  return Axios.get(`/city/getBy`, { params: { provinceCode } })
}

// 修改用户状态
export const changeUserStatus = (status) => {
  return Axios.post("/userstatus/change", null, {
    params: {
      status
    }
  })
}

// 获取好友基本信息
export const getFriendInfo = (userId) => {
  return Axios.get(`/user/getUserBaseInfo`, { params: { userId } })
}